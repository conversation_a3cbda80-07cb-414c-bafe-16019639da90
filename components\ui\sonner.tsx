"use client"

import { useTheme } from "next-themes"
import { Toaster as Sonner } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
        },
        // Filter out error toasts in development
        filterToasts: process.env.NODE_ENV === 'development' ? (toast) => {
          const message = toast.title?.toString() || toast.description?.toString() || '';
          const isError = message.toLowerCase().includes('error') ||
                         message.toLowerCase().includes('warning') ||
                         message.toLowerCase().includes('failed') ||
                         toast.type === 'error';
          return !isError;
        } : undefined,
      }}
      {...props}
    />
  )
}

export { Toaster }
