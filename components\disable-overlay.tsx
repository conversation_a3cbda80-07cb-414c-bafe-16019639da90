'use client';

import { useEffect } from 'react';

export default function DisableOverlay() {
  useEffect(() => {
    // Only run in development and if overlay is disabled via env var
    const shouldDisableOverlay = process.env.NODE_ENV === 'development' &&
      (process.env.NEXT_PUBLIC_DISABLE_ERROR_OVERLAY === 'true' ||
       typeof window !== 'undefined' && window.localStorage.getItem('disable-error-overlay') === 'true');

    if (shouldDisableOverlay) {
      // Disable React error overlay
      const disableReactDevOverlay = () => {
        // Method 1: Override the error overlay iframe
        const iframe = document.querySelector('iframe[data-nextjs-dialog-overlay]');
        if (iframe) {
          iframe.remove();
        }

        // Method 2: Hide any error overlay containers
        const overlays = document.querySelectorAll('[data-nextjs-dialog-overlay]');
        overlays.forEach(overlay => {
          (overlay as HTMLElement).style.display = 'none';
        });

        // Method 3: Override the error overlay styles
        const style = document.createElement('style');
        style.textContent = `
          iframe[data-nextjs-dialog-overlay],
          [data-nextjs-dialog-overlay],
          [data-nextjs-dialog],
          [data-nextjs-toast],
          .__next-dev-overlay-backdrop,
          .__next-dev-overlay-container {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
          }
        `;
        document.head.appendChild(style);
      };

      // Run immediately
      disableReactDevOverlay();

      // Set up a mutation observer to catch dynamically added overlays
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // Check if it's an error overlay
              if (
                element.hasAttribute('data-nextjs-dialog-overlay') ||
                element.hasAttribute('data-nextjs-dialog') ||
                element.hasAttribute('data-nextjs-toast') ||
                element.classList.contains('__next-dev-overlay-backdrop') ||
                element.classList.contains('__next-dev-overlay-container')
              ) {
                (element as HTMLElement).style.display = 'none';
              }

              // Check for iframe overlays
              if (element.tagName === 'IFRAME' && element.hasAttribute('data-nextjs-dialog-overlay')) {
                element.remove();
              }
            }
          });
        });
      });

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      // Override console.error to prevent some error displays
      const originalConsoleError = console.error;
      console.error = (...args) => {
        // Still log errors for debugging
        originalConsoleError.apply(console, args);
        
        // Prevent certain Next.js error overlays from showing
        const errorMessage = args[0]?.toString() || '';
        if (
          errorMessage.includes('Warning:') ||
          errorMessage.includes('React DevTools') ||
          errorMessage.includes('Download the React DevTools')
        ) {
          return; // Don't show these in overlay
        }
      };

      // Cleanup function
      return () => {
        observer.disconnect();
        console.error = originalConsoleError;
      };
    }
  }, []);

  return null; // This component doesn't render anything
}
