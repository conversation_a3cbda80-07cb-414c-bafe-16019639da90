'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Bug, RefreshCw } from 'lucide-react';
import Link from 'next/link';

export default function TestErrorOverlayPage() {
  const [shouldThrowError, setShouldThrowError] = useState(false);

  const throwError = () => {
    setShouldThrowError(true);
  };

  const throwConsoleError = () => {
    console.error('This is a test console error - should appear in console but not as overlay');
  };

  const throwWarning = () => {
    console.warn('This is a test warning - should appear in console but not as overlay');
  };

  const throwUncaughtError = () => {
    // This will throw an uncaught error
    setTimeout(() => {
      throw new Error('This is an uncaught error for testing overlay suppression');
    }, 100);
  };

  // This will trigger the error boundary
  if (shouldThrowError) {
    throw new Error('This is a test error thrown by the component to test error boundary');
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      <main className="flex-1 flex flex-col p-6 max-w-md mx-auto w-full">
        <div className="space-y-6">
          <div className="flex items-center mb-6">
            <Link href="/" className="text-green-800">
              <RefreshCw className="h-6 w-6" />
            </Link>
            <h1 className="text-2xl font-bold text-green-800 ml-4">Test Error Overlay</h1>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-yellow-800">Testing Error Overlay Suppression</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  Use the buttons below to test different types of errors. 
                  If the overlay suppression is working correctly, you should see errors 
                  in the browser console but no error overlay popups.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-800 mb-2 flex items-center">
                <Bug className="h-4 w-4 mr-2" />
                Console Errors (No Overlay)
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                These should appear in console but not trigger error overlays.
              </p>
              <div className="space-y-2">
                <Button
                  onClick={throwConsoleError}
                  variant="outline"
                  className="w-full border-red-200 text-red-600 hover:bg-red-50"
                >
                  Throw Console Error
                </Button>
                <Button
                  onClick={throwWarning}
                  variant="outline"
                  className="w-full border-yellow-200 text-yellow-600 hover:bg-yellow-50"
                >
                  Throw Console Warning
                </Button>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-800 mb-2 flex items-center">
                <Bug className="h-4 w-4 mr-2" />
                Component Errors (Error Boundary)
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                This will trigger the error boundary instead of showing an overlay.
              </p>
              <Button
                onClick={throwError}
                variant="outline"
                className="w-full border-red-200 text-red-600 hover:bg-red-50"
              >
                Throw Component Error
              </Button>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-800 mb-2 flex items-center">
                <Bug className="h-4 w-4 mr-2" />
                Uncaught Errors
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                This will throw an uncaught error that should be suppressed.
              </p>
              <Button
                onClick={throwUncaughtError}
                variant="outline"
                className="w-full border-red-200 text-red-600 hover:bg-red-50"
              >
                Throw Uncaught Error
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-800 mb-2">Dev Utils Available</h3>
            <p className="text-sm text-blue-700 mb-2">
              Open browser console and try these commands:
            </p>
            <div className="text-xs font-mono bg-blue-100 p-2 rounded text-blue-800">
              <div>devUtils.getOverlayStatus()</div>
              <div>devUtils.toggleErrorOverlay(false)</div>
              <div>devUtils.logError(new Error('test'))</div>
            </div>
          </div>

          <div className="text-center">
            <Link href="/">
              <Button className="bg-green-500 hover:bg-green-600">
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
