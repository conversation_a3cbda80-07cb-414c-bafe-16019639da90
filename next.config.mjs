/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Disable React error overlay in development
  reactStrictMode: false,
  // Custom webpack configuration to disable error overlay
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Disable the error overlay
      config.resolve.alias = {
        ...config.resolve.alias,
        '@next/react-dev-overlay': false,
      };

      // Additional overlay suppression
      config.resolve.fallback = {
        ...config.resolve.fallback,
        '@next/react-dev-overlay': false,
      };
    }
    return config;
  },

  // Experimental features to suppress overlays
  experimental: {
    // Disable build activity indicator
    optimizeCss: false,
  },
  // Disable development indicators
  devIndicators: {
    position: 'bottom-right',
  },
}

export default nextConfig
